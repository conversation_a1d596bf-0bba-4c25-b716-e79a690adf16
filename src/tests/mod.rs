use halo_model::ExtensionOperator;

use crate::{
    extension::index::index::query::{
        index_attribute::IndexAttributeFactory,
        index_spec::{IndexSpec, OrderType},
    },
    tests::model::fake_extension::FakeExtension,
};

pub mod model;

pub fn primary_key_index_spec() -> IndexSpec<FakeExtension> {
    IndexSpec::new(
        "metadata.name".to_string(),
        OrderType::ASC,
        true,
        IndexAttributeFactory::simple_attribute(
            "FakeExtension".to_string(),
            |e: &FakeExtension| e.get_metadata().get_name(),
        ),
    )
}

pub fn contains_entry<'a, I>(mut iter: I, key: &str, value: &str) -> bool
where
    I: Iterator<Item = (&'a String, &'a String)>,
{
    iter.any(|(k, v)| k == key && v == value)
}

