use std::cmp::Ordering;
use std::collections::BTreeMap;
use std::fmt;

pub struct ComparatorMap<K, V, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    map: BTreeMap<KeyWrapper<K, F>, V>,
    cmp: F,
}

#[derive(Clone)]
struct KeyWrapper<K, F>(K, F)
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering + Clone;


impl<K, F> PartialEq for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn eq(&self, other: &Self) -> bool {
        (self.1)(&self.0, &other.0) == Ordering::Equal
    }
}
impl<K, F> Eq for KeyWrapper<K, F> where F: Fn(&K, &K) -> Ordering {}

impl<K, F> PartialOrd for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some((self.1)(&self.0, &other.0))
    }
}

impl<K, F> Ord for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn cmp(&self, other: &Self) -> Ordering {
        (self.1)(&self.0, &other.0)
    }
}


impl<K, V, F> ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering + Clone,
{
    // 创建一个新的 ComparatorMap
    pub fn new(cmp: F) -> Self {
        ComparatorMap {
            map: BTreeMap::new(),
            cmp,
        }
    }

    // 插入键值对，返回旧值（如果存在）
    pub fn insert(&mut self, key: K, value: V) -> Option<V> {
        let wrapper = KeyWrapper(key, self.cmp.clone());
        self.map.insert(wrapper, value)
    }

    // 获取指定键的值的引用
    pub fn get(&self, key: &K) -> Option<&V> {
        // 直接遍历查找匹配的键
        self.map.iter()
            .find(|(k, _)| (self.cmp)(&k.0, key) == Ordering::Equal)
            .map(|(_, v)| v)
    }

    // 获取指定键的值的可变引用
    pub fn get_mut(&mut self, key: &K) -> Option<&mut V> {
        // 由于借用检查器的限制，我们需要先找到键，然后获取可变引用
        let cmp = &self.cmp;
        let found_key = self.map.keys()
            .find(|k| (cmp)(&k.0, key) == Ordering::Equal)
            .cloned();

        if let Some(found_key) = found_key {
            self.map.get_mut(&found_key)
        } else {
            None
        }
    }

    // 移除指定键的键值对，返回值（如果存在）
    pub fn remove(&mut self, key: &K) -> Option<V> {
        let cmp = &self.cmp;
        let found_key = self.map.keys()
            .find(|k| (cmp)(&k.0, key) == Ordering::Equal)
            .cloned();

        if let Some(found_key) = found_key {
            self.map.remove(&found_key)
        } else {
            None
        }
    }

    // 检查是否包含指定键
    pub fn contains_key(&self, key: &K) -> bool {
        self.map.keys()
            .any(|k| (self.cmp)(&k.0, key) == Ordering::Equal)
    }

    // 返回 map 中键值对的数量
    pub fn len(&self) -> usize {
        self.map.len()
    }

    // 检查 map 是否为空
    pub fn is_empty(&self) -> bool {
        self.map.is_empty()
    }

    // 清空 map
    pub fn clear(&mut self) {
        self.map.clear();
    }

    // 获取所有键的迭代器
    pub fn keys(&self) -> impl Iterator<Item = &K> {
        self.map.keys().map(|wrapper| &wrapper.0)
    }

    // 获取所有值的迭代器
    pub fn values(&self) -> impl Iterator<Item = &V> {
        self.map.values()
    }

    // 获取所有值的可变迭代器
    pub fn values_mut(&mut self) -> impl Iterator<Item = &mut V> {
        self.map.values_mut()
    }

    // 获取所有键值对的迭代器
    pub fn iter(&self) -> impl Iterator<Item = (&K, &V)> {
        self.map.iter().map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 获取所有键值对的可变迭代器
    pub fn iter_mut(&mut self) -> impl Iterator<Item = (&K, &mut V)> {
        self.map.iter_mut().map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 获取或插入默认值
    pub fn get_or_insert(&mut self, key: K, default: V) -> &mut V
    where
        K: Clone,
    {
        if !self.contains_key(&key) {
            self.insert(key.clone(), default);
        }
        self.get_mut(&key).unwrap()
    }

    // 获取或插入通过闭包计算的默认值
    pub fn get_or_insert_with<F2>(&mut self, key: K, default: F2) -> &mut V
    where
        K: Clone,
        F2: FnOnce() -> V,
    {
        if !self.contains_key(&key) {
            self.insert(key.clone(), default());
        }
        self.get_mut(&key).unwrap()
    }

    // 保留满足条件的键值对
    pub fn retain<P>(&mut self, mut predicate: P)
    where
        P: FnMut(&K, &mut V) -> bool,
    {
        self.map.retain(|wrapper, value| predicate(&wrapper.0, value));
    }

    // 扩展 map，插入另一个迭代器中的所有键值对
    pub fn extend<I>(&mut self, iter: I)
    where
        I: IntoIterator<Item = (K, V)>,
    {
        for (key, value) in iter {
            self.insert(key, value);
        }
    }

    // 获取第一个键值对的引用（按比较器排序）
    pub fn first_key_value(&self) -> Option<(&K, &V)> {
        self.map.first_key_value()
            .map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 获取最后一个键值对的引用（按比较器排序）
    pub fn last_key_value(&self) -> Option<(&K, &V)> {
        self.map.last_key_value()
            .map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 移除并返回第一个键值对（按比较器排序）
    pub fn pop_first(&mut self) -> Option<(K, V)> {
        self.map.pop_first()
            .map(|(wrapper, value)| (wrapper.0, value))
    }

    // 移除并返回最后一个键值对（按比较器排序）
    pub fn pop_last(&mut self) -> Option<(K, V)> {
        self.map.pop_last()
            .map(|(wrapper, value)| (wrapper.0, value))
    }
}

// 实现 Debug trait
impl<K, V, F> fmt::Debug for ComparatorMap<K, V, F>
where
    K: Clone + fmt::Debug,
    V: fmt::Debug,
    F: Fn(&K, &K) -> Ordering + Clone,
{
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_map()
            .entries(self.iter())
            .finish()
    }
}

// 实现 Clone trait
impl<K, V, F> Clone for ComparatorMap<K, V, F>
where
    K: Clone,
    V: Clone,
    F: Fn(&K, &K) -> Ordering + Clone,
{
    fn clone(&self) -> Self {
        ComparatorMap {
            map: self.map.clone(),
            cmp: self.cmp.clone(),
        }
    }
}

// 实现 PartialEq trait
impl<K, V, F> PartialEq for ComparatorMap<K, V, F>
where
    K: Clone,
    V: PartialEq,
    F: Fn(&K, &K) -> Ordering + Clone,
{
    fn eq(&self, other: &Self) -> bool {
        if self.len() != other.len() {
            return false;
        }

        self.iter().all(|(key, value)| {
            other.get(key).map_or(false, |v| v == value)
        })
    }
}

impl<K, V, F> Eq for ComparatorMap<K, V, F>
where
    K: Clone,
    V: Eq,
    F: Fn(&K, &K) -> Ordering + Clone,
{
}

// 实现 FromIterator trait
impl<K, V, F> std::iter::FromIterator<(K, V)> for ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering + Clone + Default,
{
    fn from_iter<I: IntoIterator<Item = (K, V)>>(iter: I) -> Self {
        let mut map = ComparatorMap::new(F::default());
        map.extend(iter);
        map
    }
}

// 实现 IntoIterator trait
impl<K, V, F> IntoIterator for ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering + Clone,
{
    type Item = (K, V);
    type IntoIter = std::iter::Map<
        std::collections::btree_map::IntoIter<KeyWrapper<K, F>, V>,
        fn((KeyWrapper<K, F>, V)) -> (K, V),
    >;

    fn into_iter(self) -> Self::IntoIter {
        self.map.into_iter().map(|(wrapper, value)| (wrapper.0, value))
    }
}

// 为引用实现 IntoIterator trait
impl<'a, K, V, F> IntoIterator for &'a ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering + Clone,
{
    type Item = (&'a K, &'a V);
    type IntoIter = std::iter::Map<
        std::collections::btree_map::Iter<'a, KeyWrapper<K, F>, V>,
        fn((&'a KeyWrapper<K, F>, &'a V)) -> (&'a K, &'a V),
    >;

    fn into_iter(self) -> Self::IntoIter {
        self.map.iter().map(|(wrapper, value)| (&wrapper.0, value))
    }
}

// 为可变引用实现 IntoIterator trait
impl<'a, K, V, F> IntoIterator for &'a mut ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering + Clone,
{
    type Item = (&'a K, &'a mut V);
    type IntoIter = std::iter::Map<
        std::collections::btree_map::IterMut<'a, KeyWrapper<K, F>, V>,
        fn((&'a KeyWrapper<K, F>, &'a mut V)) -> (&'a K, &'a mut V),
    >;

    fn into_iter(self) -> Self::IntoIter {
        self.map.iter_mut().map(|(wrapper, value)| (&wrapper.0, value))
    }
}