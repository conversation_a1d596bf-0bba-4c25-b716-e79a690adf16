use std::cmp::Ordering;
use std::collections::BTreeMap;

struct ComparatorMap<K, V, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    map: BTreeMap<KeyWrapper<K, F>, V>,
    cmp: F,
}

struct KeyWrapper<K, F>(K, F)
where
    F: Fn(&K, &K) -> Ordering;


impl<K, F> PartialEq for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn eq(&self, other: &Self) -> bool {
        (self.1)(&self.0, &other.0) == Ordering::Equal
    }
}
impl<K, F> Eq for KeyWrapper<K, F> where F: Fn(&K, &K) -> Ordering {}

impl<K, F> PartialOrd for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some((self.1)(&self.0, &other.0))
    }
}

impl<K, F> Ord for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn cmp(&self, other: &Self) -> Ordering {
        (self.1)(&self.0, &other.0)
    }
}

// ... existing code ...

impl<K, V, F> ComparatorMap<K, V, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    // 创建一个新的 ComparatorMap
    pub fn new(cmp: F) -> Self {
        ComparatorMap {
            map: BTreeMap::new(),
            cmp,
        }
    }

    // 获取指定键的值
    pub fn get(&self, key: &K) -> Option<&V> {
        let wrapped_key = KeyWrapper(key, &self.cmp);
        self.map.get(&wrapped_key)
    }

    // 插入键值对
    pub fn insert(&mut self, key: K, value: V) -> Option<V> {
        let wrapped_key = KeyWrapper(key, &self.cmp);
        self.map.insert(wrapped_key, value)
    }

    // 删除指定键的值
    pub fn remove(&mut self, key: &K) -> Option<V> {
        let wrapped_key = KeyWrapper(key, &self.cmp);
        self.map.remove(&wrapped_key)
    }
}