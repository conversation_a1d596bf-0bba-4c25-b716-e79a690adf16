use std::cmp::Ordering;
use std::collections::BTreeMap;
use std::fmt;
use std::rc::Rc;

pub struct ComparatorMap<K, V, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    map: BTreeMap<KeyWrapper<K, F>, V>,
    cmp: Rc<F>,
}

struct KeyWrapper<K, F>(K, Rc<F>)
where
    F: Fn(&K, &K) -> Ordering;

impl<K, F> Clone for KeyWrapper<K, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering,
{
    fn clone(&self) -> Self {
        KeyWrapper(self.0.clone(), self.1.clone())
    }
}


impl<K, F> PartialEq for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn eq(&self, other: &Self) -> bool {
        (self.1)(&self.0, &other.0) == Ordering::Equal
    }
}
impl<K, F> Eq for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{}

impl<K, F> PartialOrd for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some((self.1)(&self.0, &other.0))
    }
}

impl<K, F> Ord for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn cmp(&self, other: &Self) -> Ordering {
        (self.1)(&self.0, &other.0)
    }
}


impl<K, V, F> ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering,
{
    // 创建一个新的 ComparatorMap
    pub fn new(cmp: F) -> Self {
        ComparatorMap {
            map: BTreeMap::new(),
            cmp: Rc::new(cmp),
        }
    }

    // 插入键值对，返回旧值（如果存在）
    pub fn insert(&mut self, key: K, value: V) -> Option<V> {
        let wrapper = KeyWrapper(key, self.cmp.clone());
        self.map.insert(wrapper, value)
    }

    // 获取指定键的值的引用
    pub fn get(&self, key: &K) -> Option<&V> {
        // 直接遍历查找匹配的键
        self.map.iter()
            .find(|(k, _)| (self.cmp)(&k.0, key) == Ordering::Equal)
            .map(|(_, v)| v)
    }

    // 获取指定键的值的可变引用
    pub fn get_mut(&mut self, key: &K) -> Option<&mut V> {
        // 由于借用检查器的限制，我们需要先找到键，然后获取可变引用
        let cmp = &self.cmp;
        let found_key = self.map.keys()
            .find(|k| (cmp)(&k.0, key) == Ordering::Equal)
            .cloned();

        if let Some(found_key) = found_key {
            self.map.get_mut(&found_key)
        } else {
            None
        }
    }

    // 移除指定键的键值对，返回值（如果存在）
    pub fn remove(&mut self, key: &K) -> Option<V> {
        let cmp = &self.cmp;
        let found_key = self.map.keys()
            .find(|k| (cmp)(&k.0, key) == Ordering::Equal)
            .cloned();

        if let Some(found_key) = found_key {
            self.map.remove(&found_key)
        } else {
            None
        }
    }

    // 检查是否包含指定键
    pub fn contains_key(&self, key: &K) -> bool {
        self.map.keys()
            .any(|k| (self.cmp)(&k.0, key) == Ordering::Equal)
    }

    // 返回 map 中键值对的数量
    pub fn len(&self) -> usize {
        self.map.len()
    }

    // 检查 map 是否为空
    pub fn is_empty(&self) -> bool {
        self.map.is_empty()
    }

    // 清空 map
    pub fn clear(&mut self) {
        self.map.clear();
    }

    // 获取所有键的迭代器
    pub fn keys(&self) -> impl Iterator<Item = &K> {
        self.map.keys().map(|wrapper| &wrapper.0)
    }

    // 获取所有值的迭代器
    pub fn values(&self) -> impl Iterator<Item = &V> {
        self.map.values()
    }

    // 获取所有值的可变迭代器
    pub fn values_mut(&mut self) -> impl Iterator<Item = &mut V> {
        self.map.values_mut()
    }

    // 获取所有键值对的迭代器
    pub fn iter(&self) -> impl Iterator<Item = (&K, &V)> {
        self.map.iter().map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 获取所有键值对的可变迭代器
    pub fn iter_mut(&mut self) -> impl Iterator<Item = (&K, &mut V)> {
        self.map.iter_mut().map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 获取或插入默认值
    pub fn get_or_insert(&mut self, key: K, default: V) -> &mut V
    where
        K: Clone,
    {
        if !self.contains_key(&key) {
            self.insert(key.clone(), default);
        }
        self.get_mut(&key).unwrap()
    }

    // 获取或插入通过闭包计算的默认值
    pub fn get_or_insert_with<F2>(&mut self, key: K, default: F2) -> &mut V
    where
        K: Clone,
        F2: FnOnce() -> V,
    {
        if !self.contains_key(&key) {
            self.insert(key.clone(), default());
        }
        self.get_mut(&key).unwrap()
    }

    // 保留满足条件的键值对
    pub fn retain<P>(&mut self, mut predicate: P)
    where
        P: FnMut(&K, &mut V) -> bool,
    {
        self.map.retain(|wrapper, value| predicate(&wrapper.0, value));
    }

    // 扩展 map，插入另一个迭代器中的所有键值对
    pub fn extend<I>(&mut self, iter: I)
    where
        I: IntoIterator<Item = (K, V)>,
    {
        for (key, value) in iter {
            self.insert(key, value);
        }
    }

    // 获取第一个键值对的引用（按比较器排序）
    pub fn first_key_value(&self) -> Option<(&K, &V)> {
        self.map.first_key_value()
            .map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 获取最后一个键值对的引用（按比较器排序）
    pub fn last_key_value(&self) -> Option<(&K, &V)> {
        self.map.last_key_value()
            .map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 移除并返回第一个键值对（按比较器排序）
    pub fn pop_first(&mut self) -> Option<(K, V)> {
        self.map.pop_first()
            .map(|(wrapper, value)| (wrapper.0, value))
    }

    // 移除并返回最后一个键值对（按比较器排序）
    pub fn pop_last(&mut self) -> Option<(K, V)> {
        self.map.pop_last()
            .map(|(wrapper, value)| (wrapper.0, value))
    }
}

// 实现 Debug trait
impl<K, V, F> fmt::Debug for ComparatorMap<K, V, F>
where
    K: Clone + fmt::Debug,
    V: fmt::Debug,
    F: Fn(&K, &K) -> Ordering,
{
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_map()
            .entries(self.iter())
            .finish()
    }
}

// 实现 Clone trait
impl<K, V, F> Clone for ComparatorMap<K, V, F>
where
    K: Clone,
    V: Clone,
    F: Fn(&K, &K) -> Ordering,
{
    fn clone(&self) -> Self {
        ComparatorMap {
            map: self.map.clone(),
            cmp: self.cmp.clone(),
        }
    }
}

// 实现 PartialEq trait
impl<K, V, F> PartialEq for ComparatorMap<K, V, F>
where
    K: Clone,
    V: PartialEq,
    F: Fn(&K, &K) -> Ordering,
{
    fn eq(&self, other: &Self) -> bool {
        if self.len() != other.len() {
            return false;
        }

        self.iter().all(|(key, value)| {
            other.get(key).map_or(false, |v| v == value)
        })
    }
}

impl<K, V, F> Eq for ComparatorMap<K, V, F>
where
    K: Clone,
    V: Eq,
    F: Fn(&K, &K) -> Ordering,
{
}

// 实现 FromIterator trait
impl<K, V, F> std::iter::FromIterator<(K, V)> for ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering + Default,
{
    fn from_iter<I: IntoIterator<Item = (K, V)>>(iter: I) -> Self {
        let mut map = ComparatorMap::new(F::default());
        map.extend(iter);
        map
    }
}

// 实现 IntoIterator trait - 使用 Box 来避免暴露私有类型
impl<K, V, F> IntoIterator for ComparatorMap<K, V, F>
where
    K: Clone + 'static,
    V: 'static,
    F: Fn(&K, &K) -> Ordering + 'static,
{
    type Item = (K, V);
    type IntoIter = Box<dyn Iterator<Item = (K, V)>>;

    fn into_iter(self) -> Self::IntoIter {
        Box::new(self.map.into_iter().map(|(wrapper, value)| (wrapper.0, value)))
    }
}

// 为引用实现 IntoIterator trait
impl<'a, K, V, F> IntoIterator for &'a ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering,
{
    type Item = (&'a K, &'a V);
    type IntoIter = Box<dyn Iterator<Item = (&'a K, &'a V)> + 'a>;

    fn into_iter(self) -> Self::IntoIter {
        Box::new(self.map.iter().map(|(wrapper, value)| (&wrapper.0, value)))
    }
}

// 为可变引用实现 IntoIterator trait
impl<'a, K, V, F> IntoIterator for &'a mut ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering,
{
    type Item = (&'a K, &'a mut V);
    type IntoIter = Box<dyn Iterator<Item = (&'a K, &'a mut V)> + 'a>;

    fn into_iter(self) -> Self::IntoIter {
        Box::new(self.map.iter_mut().map(|(wrapper, value)| (&wrapper.0, value)))
    }
}

#[cfg(test)]
mod tests{
    use super::*;

// 自定义比较器：按字符串长度排序
fn string_length_cmp(a: &String, b: &String) -> Ordering {
    a.len().cmp(&b.len()).then_with(|| a.cmp(b))
}

// 自定义比较器：逆序整数
fn reverse_int_cmp(a: &i32, b: &i32) -> Ordering {
    b.cmp(a)
}
#[test]

pub fn test_basic_operations() {
    println!("=== 测试基本操作 ===");
    
    let mut map = ComparatorMap::new(string_length_cmp);
    
    // 测试插入
    assert_eq!(map.insert("hello".to_string(), 1), None);
    assert_eq!(map.insert("hi".to_string(), 2), None);
    assert_eq!(map.insert("world".to_string(), 3), None);
    
    // 测试长度
    assert_eq!(map.len(), 3);
    assert!(!map.is_empty());
    
    // 测试获取
    assert_eq!(map.get(&"hello".to_string()), Some(&1));
    assert_eq!(map.get(&"hi".to_string()), Some(&2));
    assert_eq!(map.get(&"world".to_string()), Some(&3));
    assert_eq!(map.get(&"missing".to_string()), None);
    
    // 测试包含键
    assert!(map.contains_key(&"hello".to_string()));
    assert!(!map.contains_key(&"missing".to_string()));
    
    println!("✅ 基本操作测试通过");
}
#[test]

pub fn test_ordering() {
    println!("=== 测试排序功能 ===");
    
    let mut map = ComparatorMap::new(reverse_int_cmp);
    
    map.insert(1, "one");
    map.insert(3, "three");
    map.insert(2, "two");
    map.insert(5, "five");
    map.insert(4, "four");
    
    // 收集键，应该按逆序排列
    let keys: Vec<_> = map.keys().cloned().collect();
    assert_eq!(keys, vec![5, 4, 3, 2, 1]);
    
    // 测试第一个和最后一个
    assert_eq!(map.first_key_value(), Some((&5, &"five")));
    assert_eq!(map.last_key_value(), Some((&1, &"one")));
    
    println!("✅ 排序功能测试通过");
}
#[test]

pub fn test_update_and_remove() {
    println!("=== 测试更新和删除 ===");
    
    let mut map = ComparatorMap::new(string_length_cmp);
    
    map.insert("hello".to_string(), 1);
    map.insert("hi".to_string(), 2);
    
    // 测试更新
    assert_eq!(map.insert("hello".to_string(), 10), Some(1));
    assert_eq!(map.get(&"hello".to_string()), Some(&10));
    
    // 测试可变引用
    if let Some(value) = map.get_mut(&"hi".to_string()) {
        *value = 20;
    }
    assert_eq!(map.get(&"hi".to_string()), Some(&20));
    
    // 测试移除
    assert_eq!(map.remove(&"hello".to_string()), Some(10));
    assert_eq!(map.remove(&"hello".to_string()), None);
    assert_eq!(map.len(), 1);
    
    println!("✅ 更新和删除测试通过");
}
#[test]

pub fn test_iterators() {
    println!("=== 测试迭代器 ===");
    
    let mut map = ComparatorMap::new(string_length_cmp);
    
    map.insert("a".to_string(), 1);
    map.insert("bb".to_string(), 2);
    map.insert("ccc".to_string(), 3);
    
    // 测试键迭代器
    let keys: Vec<_> = map.keys().cloned().collect();
    assert_eq!(keys, vec!["a".to_string(), "bb".to_string(), "ccc".to_string()]);
    
    // 测试值迭代器
    let values: Vec<_> = map.values().cloned().collect();
    assert_eq!(values, vec![1, 2, 3]);
    
    // 测试键值对迭代器
    let pairs: Vec<_> = map.iter().map(|(k, v)| (k.clone(), *v)).collect();
    assert_eq!(pairs, vec![
        ("a".to_string(), 1),
        ("bb".to_string(), 2),
        ("ccc".to_string(), 3)
    ]);
    
    println!("✅ 迭代器测试通过");
}
#[test]

pub fn test_utility_methods() {
    println!("=== 测试实用方法 ===");
    
    let mut map = ComparatorMap::new(string_length_cmp);
    
    // 测试 get_or_insert
    let value = map.get_or_insert("hello".to_string(), 42);
    assert_eq!(*value, 42);
    assert_eq!(map.len(), 1);
    
    // 再次调用应该返回现有值
    let value = map.get_or_insert("hello".to_string(), 100);
    assert_eq!(*value, 42);
    assert_eq!(map.len(), 1);
    
    // 测试 get_or_insert_with
    let value = map.get_or_insert_with("world".to_string(), || 99);
    assert_eq!(*value, 99);
    assert_eq!(map.len(), 2);
    
    println!("✅ 实用方法测试通过");
}
#[test]

pub fn test_retain() {
    println!("=== 测试 retain 方法 ===");
    
    let mut map = ComparatorMap::new(string_length_cmp);
    
    map.insert("a".to_string(), 1);
    map.insert("bb".to_string(), 2);
    map.insert("ccc".to_string(), 3);
    map.insert("dddd".to_string(), 4);
    
    // 只保留值为偶数的项
    map.retain(|_k, v| *v % 2 == 0);
    
    assert_eq!(map.len(), 2);
    assert!(map.contains_key(&"bb".to_string()));
    assert!(map.contains_key(&"dddd".to_string()));
    assert!(!map.contains_key(&"a".to_string()));
    assert!(!map.contains_key(&"ccc".to_string()));
    
    println!("✅ retain 方法测试通过");
}
#[test]

pub fn test_pop_operations() {
    println!("=== 测试 pop 操作 ===");
    
    let mut map = ComparatorMap::new(reverse_int_cmp);
    
    map.insert(1, "one");
    map.insert(3, "three");
    map.insert(2, "two");
    
    // 弹出第一个（最大的）
    assert_eq!(map.pop_first(), Some((3, "three")));
    assert_eq!(map.len(), 2);
    
    // 弹出最后一个（最小的）
    assert_eq!(map.pop_last(), Some((1, "one")));
    assert_eq!(map.len(), 1);
    
    assert_eq!(map.pop_first(), Some((2, "two")));
    assert!(map.is_empty());
    assert_eq!(map.pop_first(), None);
    
    println!("✅ pop 操作测试通过");
}
#[test]

pub fn test_clear() {
    println!("=== 测试 clear 方法 ===");
    
    let mut map = ComparatorMap::new(string_length_cmp);
    
    map.insert("hello".to_string(), 1);
    map.insert("world".to_string(), 2);
    
    assert_eq!(map.len(), 2);
    
    map.clear();
    
    assert_eq!(map.len(), 0);
    assert!(map.is_empty());
    assert_eq!(map.get(&"hello".to_string()), None);
    
    println!("✅ clear 方法测试通过");
}

#[test]
pub fn run_all_tests() {
    println!("🚀 开始运行 ComparatorMap 测试...\n");
    
    test_basic_operations();
    test_ordering();
    test_update_and_remove();
    test_iterators();
    test_utility_methods();
    test_retain();
    test_pop_operations();
    test_clear();
    
    println!("\n🎉 所有测试都通过了！");
}


}