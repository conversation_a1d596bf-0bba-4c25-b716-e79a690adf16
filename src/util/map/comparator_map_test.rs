#[cfg(test)]
mod tests {
    use crate::util::map::comparator_map::ComparatorMap;
    use std::cmp::Ordering;

    // 自定义比较器：按字符串长度排序
    fn string_length_cmp(a: &String, b: &String) -> Ordering {
        a.len().cmp(&b.len()).then_with(|| a.cmp(b))
    }

    // 自定义比较器：逆序整数
    fn reverse_int_cmp(a: &i32, b: &i32) -> Ordering {
        b.cmp(a)
    }

    #[test]
    fn test_basic_operations() {
        let mut map = ComparatorMap::new(string_length_cmp);
        
        // 测试插入
        assert_eq!(map.insert("hello".to_string(), 1), None);
        assert_eq!(map.insert("hi".to_string(), 2), None);
        assert_eq!(map.insert("world".to_string(), 3), None);
        
        // 测试长度
        assert_eq!(map.len(), 3);
        assert!(!map.is_empty());
        
        // 测试获取
        assert_eq!(map.get(&"hello".to_string()), Some(&1));
        assert_eq!(map.get(&"hi".to_string()), Some(&2));
        assert_eq!(map.get(&"world".to_string()), Some(&3));
        assert_eq!(map.get(&"missing".to_string()), None);
        
        // 测试包含键
        assert!(map.contains_key(&"hello".to_string()));
        assert!(!map.contains_key(&"missing".to_string()));
    }

    #[test]
    fn test_update_and_remove() {
        let mut map = ComparatorMap::new(string_length_cmp);
        
        map.insert("hello".to_string(), 1);
        map.insert("hi".to_string(), 2);
        
        // 测试更新
        assert_eq!(map.insert("hello".to_string(), 10), Some(1));
        assert_eq!(map.get(&"hello".to_string()), Some(&10));
        
        // 测试可变引用
        if let Some(value) = map.get_mut(&"hi".to_string()) {
            *value = 20;
        }
        assert_eq!(map.get(&"hi".to_string()), Some(&20));
        
        // 测试移除
        assert_eq!(map.remove(&"hello".to_string()), Some(10));
        assert_eq!(map.remove(&"hello".to_string()), None);
        assert_eq!(map.len(), 1);
    }

    #[test]
    fn test_iterators() {
        let mut map = ComparatorMap::new(string_length_cmp);
        
        map.insert("a".to_string(), 1);
        map.insert("bb".to_string(), 2);
        map.insert("ccc".to_string(), 3);
        
        // 测试键迭代器
        let keys: Vec<_> = map.keys().cloned().collect();
        assert_eq!(keys, vec!["a".to_string(), "bb".to_string(), "ccc".to_string()]);
        
        // 测试值迭代器
        let values: Vec<_> = map.values().cloned().collect();
        assert_eq!(values, vec![1, 2, 3]);
        
        // 测试键值对迭代器
        let pairs: Vec<_> = map.iter().map(|(k, v)| (k.clone(), *v)).collect();
        assert_eq!(pairs, vec![
            ("a".to_string(), 1),
            ("bb".to_string(), 2),
            ("ccc".to_string(), 3)
        ]);
    }

    #[test]
    fn test_reverse_ordering() {
        let mut map = ComparatorMap::new(reverse_int_cmp);
        
        map.insert(1, "one");
        map.insert(3, "three");
        map.insert(2, "two");
        
        // 应该按逆序排列
        let keys: Vec<_> = map.keys().cloned().collect();
        assert_eq!(keys, vec![3, 2, 1]);
        
        // 测试第一个和最后一个
        assert_eq!(map.first_key_value(), Some((&3, &"three")));
        assert_eq!(map.last_key_value(), Some((&1, &"one")));
    }

    #[test]
    fn test_pop_operations() {
        let mut map = ComparatorMap::new(reverse_int_cmp);
        
        map.insert(1, "one");
        map.insert(3, "three");
        map.insert(2, "two");
        
        // 弹出第一个（最大的）
        assert_eq!(map.pop_first(), Some((3, "three")));
        assert_eq!(map.len(), 2);
        
        // 弹出最后一个（最小的）
        assert_eq!(map.pop_last(), Some((1, "one")));
        assert_eq!(map.len(), 1);
        
        assert_eq!(map.pop_first(), Some((2, "two")));
        assert!(map.is_empty());
        assert_eq!(map.pop_first(), None);
    }

    #[test]
    fn test_utility_methods() {
        let mut map = ComparatorMap::new(string_length_cmp);
        
        // 测试 get_or_insert
        let value = map.get_or_insert("hello".to_string(), 42);
        assert_eq!(*value, 42);
        assert_eq!(map.len(), 1);
        
        // 再次调用应该返回现有值
        let value = map.get_or_insert("hello".to_string(), 100);
        assert_eq!(*value, 42);
        assert_eq!(map.len(), 1);
        
        // 测试 get_or_insert_with
        let value = map.get_or_insert_with("world".to_string(), || 99);
        assert_eq!(*value, 99);
        assert_eq!(map.len(), 2);
    }

    #[test]
    fn test_retain() {
        let mut map = ComparatorMap::new(string_length_cmp);
        
        map.insert("a".to_string(), 1);
        map.insert("bb".to_string(), 2);
        map.insert("ccc".to_string(), 3);
        map.insert("dddd".to_string(), 4);
        
        // 只保留值为偶数的项
        map.retain(|_k, v| *v % 2 == 0);
        
        assert_eq!(map.len(), 2);
        assert!(map.contains_key(&"bb".to_string()));
        assert!(map.contains_key(&"dddd".to_string()));
        assert!(!map.contains_key(&"a".to_string()));
        assert!(!map.contains_key(&"ccc".to_string()));
    }

    #[test]
    fn test_extend() {
        let mut map = ComparatorMap::new(string_length_cmp);
        
        map.insert("a".to_string(), 1);
        
        let additional = vec![
            ("bb".to_string(), 2),
            ("ccc".to_string(), 3),
        ];
        
        map.extend(additional);
        
        assert_eq!(map.len(), 3);
        assert_eq!(map.get(&"bb".to_string()), Some(&2));
        assert_eq!(map.get(&"ccc".to_string()), Some(&3));
    }

    #[test]
    fn test_clear() {
        let mut map = ComparatorMap::new(string_length_cmp);
        
        map.insert("hello".to_string(), 1);
        map.insert("world".to_string(), 2);
        
        assert_eq!(map.len(), 2);
        
        map.clear();
        
        assert_eq!(map.len(), 0);
        assert!(map.is_empty());
        assert_eq!(map.get(&"hello".to_string()), None);
    }

    #[test]
    fn test_clone_and_equality() {
        let mut map1 = ComparatorMap::new(string_length_cmp);
        map1.insert("hello".to_string(), 1);
        map1.insert("world".to_string(), 2);
        
        let map2 = map1.clone();
        
        assert_eq!(map1, map2);
        assert_eq!(map1.len(), map2.len());
        
        // 修改其中一个
        let mut map3 = map1.clone();
        map3.insert("test".to_string(), 3);
        
        assert_ne!(map1, map3);
    }
}
