use std::cmp::Ordering;

// 由于这是一个二进制项目，我们需要直接包含模块
mod util {
    pub mod map {
        pub mod comparator_map;
    }
}

use util::map::comparator_map::ComparatorMap;

// 自定义比较器：按字符串长度排序
fn string_length_cmp(a: &String, b: &String) -> Ordering {
    a.len().cmp(&b.len()).then_with(|| a.cmp(b))
}

// 自定义比较器：逆序整数
fn reverse_int_cmp(a: &i32, b: &i32) -> Ordering {
    b.cmp(a)
}

fn main() {
    println!("=== ComparatorMap Demo ===\n");

    // 演示字符串长度比较器
    println!("1. 字符串长度比较器演示:");
    let mut string_map = ComparatorMap::new(string_length_cmp);
    
    string_map.insert("hello".to_string(), 1);
    string_map.insert("hi".to_string(), 2);
    string_map.insert("world".to_string(), 3);
    string_map.insert("a".to_string(), 4);
    string_map.insert("programming".to_string(), 5);
    
    println!("插入的键值对（按长度排序）:");
    for (key, value) in string_map.iter() {
        println!("  '{}' (长度: {}) => {}", key, key.len(), value);
    }
    
    println!("第一个键值对: {:?}", string_map.first_key_value());
    println!("最后一个键值对: {:?}", string_map.last_key_value());
    println!();

    // 演示逆序整数比较器
    println!("2. 逆序整数比较器演示:");
    let mut int_map = ComparatorMap::new(reverse_int_cmp);
    
    int_map.insert(1, "one");
    int_map.insert(5, "five");
    int_map.insert(3, "three");
    int_map.insert(2, "two");
    int_map.insert(4, "four");
    
    println!("插入的键值对（逆序排列）:");
    for (key, value) in int_map.iter() {
        println!("  {} => {}", key, value);
    }
    
    println!("第一个键值对: {:?}", int_map.first_key_value());
    println!("最后一个键值对: {:?}", int_map.last_key_value());
    println!();

    // 演示基本操作
    println!("3. 基本操作演示:");
    let mut demo_map = ComparatorMap::new(string_length_cmp);
    
    // 插入
    demo_map.insert("test".to_string(), 100);
    demo_map.insert("example".to_string(), 200);
    
    // 查找
    println!("查找 'test': {:?}", demo_map.get(&"test".to_string()));
    println!("查找 'missing': {:?}", demo_map.get(&"missing".to_string()));
    
    // 更新
    demo_map.insert("test".to_string(), 150);
    println!("更新后查找 'test': {:?}", demo_map.get(&"test".to_string()));
    
    // 移除
    let removed = demo_map.remove(&"test".to_string());
    println!("移除 'test': {:?}", removed);
    println!("移除后 map 大小: {}", demo_map.len());
    println!();

    // 演示实用方法
    println!("4. 实用方法演示:");
    let mut util_map = ComparatorMap::new(string_length_cmp);
    
    // get_or_insert
    let value = util_map.get_or_insert("hello".to_string(), 42);
    println!("get_or_insert 'hello': {}", value);
    
    let value = util_map.get_or_insert("hello".to_string(), 100);
    println!("再次 get_or_insert 'hello': {}", value);
    
    // get_or_insert_with
    let value = util_map.get_or_insert_with("world".to_string(), || 99);
    println!("get_or_insert_with 'world': {}", value);
    
    println!("最终 map 内容:");
    for (key, value) in util_map.iter() {
        println!("  '{}' => {}", key, value);
    }
    println!();

    // 演示 retain 方法
    println!("5. retain 方法演示:");
    let mut retain_map = ComparatorMap::new(string_length_cmp);
    
    retain_map.insert("a".to_string(), 1);
    retain_map.insert("bb".to_string(), 2);
    retain_map.insert("ccc".to_string(), 3);
    retain_map.insert("dddd".to_string(), 4);
    retain_map.insert("eeeee".to_string(), 5);
    
    println!("retain 前:");
    for (key, value) in retain_map.iter() {
        println!("  '{}' => {}", key, value);
    }
    
    // 只保留值为偶数的项
    retain_map.retain(|_k, v| *v % 2 == 0);
    
    println!("retain 后（只保留偶数值）:");
    for (key, value) in retain_map.iter() {
        println!("  '{}' => {}", key, value);
    }
    println!();

    // 演示 pop 操作
    println!("6. pop 操作演示:");
    let mut pop_map = ComparatorMap::new(reverse_int_cmp);
    
    pop_map.insert(1, "one");
    pop_map.insert(3, "three");
    pop_map.insert(2, "two");
    pop_map.insert(5, "five");
    pop_map.insert(4, "four");
    
    println!("pop 前 map 内容:");
    for (key, value) in pop_map.iter() {
        println!("  {} => {}", key, value);
    }
    
    println!("pop_first: {:?}", pop_map.pop_first());
    println!("pop_last: {:?}", pop_map.pop_last());
    
    println!("pop 后 map 内容:");
    for (key, value) in pop_map.iter() {
        println!("  {} => {}", key, value);
    }

    println!("\n=== Demo 完成 ===");
}
